import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  await queryInterface.createTable("AgentUsage", {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },

    // Message correlation
    message_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Reference to ChatMessage for debugging",
    },
    conversation_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Reference to Conversation",
    },

    // User attribution (CRITICAL for billing)
    user_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: "User ID for billing attribution",
    },
    project_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Project ID for project-level billing",
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Organization ID for enterprise billing",
    },

    // Provider attribution (CRITICAL for multi-provider)
    provider: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: "AI provider: google, openai, anthropic",
    },
    model_id: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: "Model identifier: gpt-4o, claude-3.5-sonnet, gemini-2.5-pro",
    },
    model_tier: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: "Model tier: economy, balanced, premium",
    },
    model_version: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Model version for future tracking",
    },

    // Token tracking (precise billing data)
    input_tokens: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: "Number of input tokens processed",
    },
    output_tokens: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: "Number of output tokens generated",
    },
    image_tokens: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Number of image tokens processed",
    },
    total_tokens: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: "Total tokens (input + output + image)",
    },

    // Cost breakdown (USD with 6 decimal precision)
    input_cost_usd: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
      comment: "Cost of input tokens in USD",
    },
    output_cost_usd: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
      comment: "Cost of output tokens in USD",
    },
    image_cost_usd: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
      comment: "Cost of image processing in USD",
    },
    total_cost_usd: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
      comment: "Total cost in USD",
    },
    cost_per_token: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
      comment: "Average cost per token",
    },

    // Pricing snapshot (for historical accuracy)
    pricing_input_per_1m: {
      type: DataTypes.FLOAT,
      allowNull: false,
      comment: "Input price per 1M tokens at time of usage",
    },
    pricing_output_per_1m: {
      type: DataTypes.FLOAT,
      allowNull: false,
      comment: "Output price per 1M tokens at time of usage",
    },
    pricing_currency: {
      type: DataTypes.STRING,
      defaultValue: "USD",
      comment: "Currency for pricing",
    },

    // Context metadata
    rag_used: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: "Whether RAG (retrieval) was used",
    },
    tool_calls: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Number of tool/function calls made",
    },
    message_length: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Length of user message in characters",
    },
    context_length: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Total context length including history",
    },
    processing_time_ms: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Processing time in milliseconds",
    },

    // Quality and performance metrics
    finish_reason: {
      type: DataTypes.STRING,
      defaultValue: "stop",
      comment: "How the generation finished: stop, length, error, etc.",
    },
    error_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Number of errors encountered",
    },
    retry_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Number of retries before success",
    },
    fallback_used: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: "Whether fallback model was used",
    },
    original_model_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Original model if fallback was used",
    },

    // Request metadata
    request_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Request ID for correlation",
    },
    session_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: "Session ID for user session tracking",
    },

    // Billing periods (for fast report generation)
    billing_month: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Billing month in YYYYMM format (202412)",
    },
    billing_quarter: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Billing quarter in YYYYQ format (20244)",
    },
    billing_year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Billing year (2024)",
    },

    // Billing status
    billing_status: {
      type: DataTypes.STRING,
      defaultValue: "pending",
      comment: "Billing status: pending, processed, disputed, refunded",
    },
    billed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "When this usage was billed",
    },

    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes for performance
  await queryInterface.addIndex("AgentUsage", {
    fields: ["user_id", "billing_month", "provider"],
    name: "idx_agent_usage_billing_period",
  });

  await queryInterface.addIndex("AgentUsage", {
    fields: ["project_id", "billing_month", "total_cost_usd"],
    name: "idx_agent_usage_project_billing",
  });

  await queryInterface.addIndex("AgentUsage", {
    fields: ["provider", "model_tier", "total_cost_usd"],
    name: "idx_agent_usage_cost_analysis",
  });

  await queryInterface.addIndex("AgentUsage", {
    fields: ["user_id"],
    name: "idx_agent_usage_user_costs",
  });

  await queryInterface.addIndex("AgentUsage", {
    fields: ["provider", "user_id"],
    name: "idx_agent_usage_time_partition",
  });

  await queryInterface.addIndex("AgentUsage", {
    fields: ["message_id", "conversation_id"],
    name: "idx_agent_usage_message_lookup",
  });

  console.log(
    "✅ Created AgentUsage table with indexes for multi-provider billing tracking"
  );
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  await queryInterface.dropTable("AgentUsage");
  console.log("✅ Dropped agent_usage table");
}
