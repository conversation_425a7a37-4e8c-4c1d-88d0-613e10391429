import { db } from "../db";
import { ts } from "@/types";
import { ProviderType, ModelTier, FinishReason } from "@/types/universal-ai";

const AgentUsageModel = db.xDefine(
  "AgentUsage",
  {
    id: {
      type: "STRING",
      defaultValue: "UUIDV4",
      primaryKey: true,
    },

    // Message correlation
    messageId: {
      type: "STRING",
      allowNull: true,
      field: "message_id",
      comment: "Reference to ChatMessage for debugging",
    },
    conversationId: {
      type: "STRING",
      allowNull: true,
      field: "conversation_id",
      comment: "Reference to Conversation",
    },

    // User attribution (CRITICAL for billing)
    userId: {
      type: "STRING",
      allowNull: false,
      field: "user_id",
      comment: "User ID for billing attribution",
    },
    projectId: {
      type: "STRING",
      allowNull: true,
      field: "project_id",
      comment: "Project ID for project-level billing",
    },
    organizationId: {
      type: "STRING",
      allowNull: true,
      field: "organization_id",
      comment: "Organization ID for enterprise billing",
    },

    // Provider attribution (CRITICAL for multi-provider)
    provider: {
      type: "STRING",
      allowNull: false,
      tsType: ts<ProviderType>(),
      comment: "AI provider: google, anthropic",
      validate: {
        isIn: [["google", "anthropic", "mistral", "meta", "cohere"]],
      },
    },
    modelId: {
      type: "STRING",
      allowNull: false,
      field: "model_id",
      comment: "Model identifier: claude-3.5-sonnet, gemini-2.5-pro",
    },
    modelTier: {
      type: "STRING",
      allowNull: false,
      field: "model_tier",
      tsType: ts<ModelTier>(),
      comment: "Model tier: economy, balanced, premium",
      validate: {
        isIn: [["economy", "balanced", "premium", "enterprise"]],
      },
    },
    modelVersion: {
      type: "STRING",
      allowNull: true,
      field: "model_version",
      comment: "Model version for future tracking",
    },

    // Token tracking (precise billing data)
    inputTokens: {
      type: "INTEGER",
      allowNull: false,
      defaultValue: 0,
      field: "input_tokens",
      comment: "Number of input tokens processed",
      validate: { min: 0 },
    },
    outputTokens: {
      type: "INTEGER",
      allowNull: false,
      defaultValue: 0,
      field: "output_tokens",
      comment: "Number of output tokens generated",
      validate: { min: 0 },
    },
    imageTokens: {
      type: "INTEGER",
      defaultValue: 0,
      field: "image_tokens",
      comment: "Number of image tokens processed",
      validate: { min: 0 },
    },
    totalTokens: {
      type: "INTEGER",
      allowNull: false,
      defaultValue: 0,
      field: "total_tokens",
      comment: "Total tokens (input + output + image)",
      validate: { min: 0 },
    },

    // Cost breakdown (USD with 6 decimal precision)
    inputCostUsd: {
      type: "FLOAT",
      allowNull: false,
      defaultValue: 0,
      field: "input_cost_usd",
      comment: "Cost of input tokens in USD",
      validate: { min: 0 },
    },
    outputCostUsd: {
      type: "FLOAT",
      allowNull: false,
      defaultValue: 0,
      field: "output_cost_usd",
      comment: "Cost of output tokens in USD",
      validate: { min: 0 },
    },
    imageCostUsd: {
      type: "FLOAT",
      defaultValue: 0,
      field: "image_cost_usd",
      comment: "Cost of image processing in USD",
      validate: { min: 0 },
    },
    totalCostUsd: {
      type: "FLOAT",
      allowNull: false,
      defaultValue: 0,
      field: "total_cost_usd",
      comment: "Total cost in USD",
      validate: { min: 0 },
    },
    costPerToken: {
      type: "FLOAT",
      allowNull: false,
      defaultValue: 0,
      field: "cost_per_token",
      comment: "Average cost per token",
      validate: { min: 0 },
    },

    // Pricing snapshot (for historical accuracy)
    pricingInputPer1m: {
      type: "FLOAT",
      allowNull: false,
      field: "pricing_input_per_1m",
      comment: "Input price per 1M tokens at time of usage",
    },
    pricingOutputPer1m: {
      type: "FLOAT",
      allowNull: false,
      field: "pricing_output_per_1m",
      comment: "Output price per 1M tokens at time of usage",
    },
    pricingCurrency: {
      type: "STRING",
      defaultValue: "USD",
      field: "pricing_currency",
      comment: "Currency for pricing",
    },

    // Context metadata
    ragUsed: {
      type: "BOOLEAN",
      defaultValue: false,
      field: "rag_used",
      comment: "Whether RAG (retrieval) was used",
    },
    toolCalls: {
      type: "INTEGER",
      defaultValue: 0,
      field: "tool_calls",
      comment: "Number of tool/function calls made",
      validate: { min: 0 },
    },
    messageLength: {
      type: "INTEGER",
      defaultValue: 0,
      field: "message_length",
      comment: "Length of user message in characters",
      validate: { min: 0 },
    },
    contextLength: {
      type: "INTEGER",
      defaultValue: 0,
      field: "context_length",
      comment: "Total context length including history",
      validate: { min: 0 },
    },
    processingTimeMs: {
      type: "INTEGER",
      defaultValue: 0,
      field: "processing_time_ms",
      comment: "Processing time in milliseconds",
      validate: { min: 0 },
    },

    // Quality and performance metrics
    finishReason: {
      type: "STRING",
      defaultValue: "stop",
      field: "finish_reason",
      tsType: ts<FinishReason>(),
      comment: "How the generation finished: stop, length, error, etc.",
    },
    errorCount: {
      type: "INTEGER",
      defaultValue: 0,
      field: "error_count",
      comment: "Number of errors encountered",
      validate: { min: 0 },
    },
    retryCount: {
      type: "INTEGER",
      defaultValue: 0,
      field: "retry_count",
      comment: "Number of retries before success",
      validate: { min: 0 },
    },
    fallbackUsed: {
      type: "BOOLEAN",
      defaultValue: false,
      field: "fallback_used",
      comment: "Whether fallback model was used",
    },
    originalModelId: {
      type: "STRING",
      allowNull: true,
      field: "original_model_id",
      comment: "Original model if fallback was used",
    },

    // Request metadata
    requestId: {
      type: "STRING",
      allowNull: true,
      field: "request_id",
      comment: "Request ID for correlation",
    },
    sessionId: {
      type: "STRING",
      allowNull: true,
      field: "session_id",
      comment: "Session ID for user session tracking",
    },

    // Billing periods (for fast report generation)
    billingMonth: {
      type: "INTEGER",
      allowNull: false,
      field: "billing_month",
      comment: "Billing month in YYYYMM format (202412)",
    },
    billingQuarter: {
      type: "INTEGER",
      allowNull: false,
      field: "billing_quarter",
      comment: "Billing quarter in YYYYQ format (20244)",
    },
    billingYear: {
      type: "INTEGER",
      allowNull: false,
      field: "billing_year",
      comment: "Billing year (2024)",
    },

    // Billing status
    billingStatus: {
      type: "STRING",
      defaultValue: "pending",
      field: "billing_status",
      comment: "Billing status: pending, processed, disputed, refunded",
      validate: {
        isIn: [["pending", "processed", "disputed", "refunded"]],
      },
    },
    billedAt: {
      type: "DATE",
      allowNull: true,
      field: "billed_at",
      comment: "When this usage was billed",
    },
  },
  {
    tableName: "AgentUsage",
    paranoid: false, // No soft deletes for billing data
    indexes: [
      {
        fields: ["user_id", "billing_month", "provider"],
        name: "idx_agent_usage_billing_period",
      },
      {
        fields: ["project_id", "billing_month", "total_cost_usd"],
        name: "idx_agent_usage_project_billing",
      },
      {
        fields: ["provider", "model_tier", "total_cost_usd"],
        name: "idx_agent_usage_cost_analysis",
      },
      {
        fields: ["user_id"],
        name: "idx_agent_usage_user_costs",
      },
      {
        fields: ["provider", "user_id"],
        name: "idx_agent_usage_time_partition",
      },
      {
        fields: ["message_id", "conversation_id"],
        name: "idx_agent_usage_message_lookup",
      },
    ],
  }
);

export type AgentUsage = typeof AgentUsageModel.$M;
export type AgentUsageCreate = typeof AgentUsageModel.$C;
export type AgentUsageWhere = typeof AgentUsageModel.$W;

export default AgentUsageModel;
