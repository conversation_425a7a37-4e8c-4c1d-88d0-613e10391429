/**
 * Pinecone Search Tool - Simple implementation using new tool architecture
 *
 * This replaces the complex PineconeToolProvider with a clean, simple approach.
 * Provides AI-generated responses with structured citations from user files.
 */

import type { Tool } from "@google/genai";
import { Type } from "@google/genai";
import { BaseTool, type ToolEnablementContext } from "../BaseTool";
import type { ToolCallArgs, ToolCallResult } from "../ToolRegistry";
import type { ToolExecutionContext } from "../ToolExecutionContext";
import { handlePineconeChatToolCall } from "@/services/pinecone/contextTools";
import { FEATURE_FLAGS } from "@/services/featureFlags";

export class PineconeSearchTool extends BaseTool {
  readonly name = "pinecone_search";
  readonly description =
    "Pinecone Assistant for AI responses with structured citations";

  constructor() {
    super();
  }

  isEnabled(context: ToolEnablementContext): boolean {
    // Must have RAG search enabled by user
    if (!context.preferences?.ragSearchEnabled) {
      return false;
    }

    // Only enable if there are actually searchable files available
    if (!context.searchableFileIds || context.searchableFileIds.length === 0) {
      return false;
    }

    return !context.featureFlags[FEATURE_FLAGS.BEINGS_RAG_ENGINE];
  }

  // ============================================================================
  // TOOL DEFINITION
  // ============================================================================

  getToolDefinition(): Tool {
    return {
      functionDeclarations: [
        {
          name: "query_pinecone_chat",
          description:
            "Generates AI responses with structured citations from the user's uploaded files. Use this when you want to provide a complete answer with proper citations that show exactly where information comes from.",
          parameters: {
            type: Type.OBJECT,
            properties: {
              query: {
                type: Type.STRING,
                description:
                  "The user's question or request that you want to answer using their uploaded files. This will be converted to a chat message and sent to the AI assistant. For example: 'What are the main findings in this research?' or 'Summarize the key points from these documents'.",
              },
              fileIds: {
                type: Type.ARRAY,
                items: { type: Type.STRING },
                description:
                  "Specific file IDs to search within (use File IDs, not filenames)",
              },
              projectId: {
                type: Type.STRING,
                description: "Project ID for multi-tenant filtering",
              },
            },
            required: ["query"], // Only query is required from AI - other params are injected by system
          },
        },
      ],
    };
  }

  // ============================================================================
  // EXECUTION
  // ============================================================================

  async execute(
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult> {
    const startTime = Date.now();
    this.logStart("query_pinecone_chat", context, args);

    // Validate required arguments
    const validation = this.validateArgs(args, ["query"]);
    if (!validation.valid) {
      this.logError(
        "query_pinecone_chat",
        context,
        validation.error!,
        Date.now() - startTime
      );
      return this.createErrorResult(validation.error!);
    }

    const { query, fileIds, projectId } = args;

    try {
      // Prepare arguments for Pinecone with proper type casting
      const pineconeArgs = {
        query: String(query),
        fileIds: Array.isArray(fileIds) ? fileIds.map(String) : [],
        projectId: projectId ? String(projectId) : undefined,
      };

      // Call the existing Pinecone handler
      const result = await handlePineconeChatToolCall(
        "query_pinecone_chat",
        pineconeArgs
      );
      const processingTime = Date.now() - startTime;

      if (!result.success) {
        this.logError(
          "query_pinecone_chat",
          context,
          result.error || result.message,
          processingTime
        );
        return this.createErrorResult(
          result.error || result.message || "Pinecone search failed",
          { processingTime }
        );
      }

      this.logComplete("query_pinecone_chat", context, true, processingTime, {
        responseLength: result.response?.length || 0,
        citationCount: result.citations?.length || 0,
        filesSearched: pineconeArgs.fileIds.length,
      });

      return this.createSuccessResult(result, {
        processingTime,
        responseLength: result.response?.length || 0,
        citationCount: result.citations?.length || 0,
        filesSearched: pineconeArgs.fileIds.length,
        source: "pinecone-chat",
      });
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logError("query_pinecone_chat", context, error, processingTime);

      return this.createErrorResult(
        error instanceof Error ? error.message : "Unknown error occurred",
        { processingTime }
      );
    }
  }

  // ============================================================================
  // HEALTH CHECK
  // ============================================================================

  async healthCheck(): Promise<boolean> {
    try {
      // Health check focuses on tool readiness, not feature flags
      // Feature flag filtering is handled by ContextManager

      // Pinecone context tools are stateless - they're always healthy if configured
      // Could add actual Pinecone API connectivity check here if needed:
      // const isReachable = await this.pingPineconeAPI();
      // return isReachable;

      return true;
    } catch (error) {
      this.logger.error("Health check failed", {
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }
}
