// ============================================================================
// CHAT API TYPES (NEW - FOR CITATIONS)
// ============================================================================

import { IChatMessage, TokenUsage } from "@/types/agent";

// Pinecone Chat API message format - subset of IChatMessage
export type ChatMessage = Pick<IChatMessage, "role" | "content">;

// Pinecone's raw token usage format (as returned by API)
export interface PineconeTokenUsage {
  completion_tokens: number;
  prompt_tokens: number;
  total_tokens: number;
}

// Pinecone's raw chat response format (as returned by API)
export interface PineconeChatResponse {
  citations: ChatCitation[];
  finish_reason: string;
  id: string;
  message: {
    content: string;
    role: string;
  };
  model: string;
  usage: PineconeTokenUsage;
}

// Our standardized chat response format
export interface ChatResponse {
  citations: ChatCitation[];
  finish_reason: string;
  id: string;
  message: {
    content: string;
    role: string;
  };
  model: string;
  usage: TokenUsage;
}

// Pinecone's citation reference structure (from API)
export interface ChatCitationReference {
  file: {
    id: string;
    name: string;
    created_on: string;
    updated_on: string;
    status: string;
    size: number;
    metadata: any;
    percent_done: number;
    signed_url?: string;
  };
  pages: number[];
  highlight: {
    type: string;
    content: string;
  };
}
// Pinecone's citation structure (from API)
export interface ChatCitation {
  position: number;
  references: Array<ChatCitationReference>;
}

// Structured citation format for tool responses
export interface Citation {
  id: string;
  position: number;
  references: Array<{
    id: string;
    file: {
      id: string;
      name: string;
      displayName: string;
      metadata: Record<string, any>;
    };
    pages: number[];
    text?: string; // Snippet text or description
    // Optional transcript-specific fields
    type?: "transcript" | "document";
    speakers?: string[];
    duration?: number; // Audio duration in seconds
    source?: string; // Transcript source (e.g., 'revai')
    timestamp?: string; // Extracted timestamp from transcript content (HH:MM:SS)
    timestampFormatted?: string; // Human-readable timestamp format
    timestampSeconds?: number; // Timestamp in seconds for video seeking
    timestampConfidence?: "high" | "medium" | "low"; // Confidence level of timestamp extraction
    currentSpeaker?: string; // Speaker at this citation position
  }>;
}

// Tool response structure with citations
export interface ToolResponseWithCitations {
  success: boolean;
  citationCount: number;
  message: string;
  content: string;
  citations: Citation[];
  metadata?: {
    searchQuery: string;
    filesSearched: number | string;
    citationCount: number;
    finishReason: string;
    model: string;
    usage: TokenUsage;
  };
  error?: string;
}

export interface ChatQueryOptions {
  messages: ChatMessage[];
  fileIds?: string[];
  userId?: string;
  projectId?: string;
  streaming?: boolean;
  filter?: Record<string, any>;
  model?: string; // Optional model parameter for Pinecone
}
