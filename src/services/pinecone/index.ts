import {
  pineconeApiKey,
  pineconeAssistantHost,
  pineconeAssistantName,
} from "@/config";
import { WinstonLogger } from "@/services/logger/winston";
import {
  UnifiedDocumentPayload,
  UnifiedDocumentMetadata,
  PlatformUploadResult,
} from "@/types/vectorDb";
import { TokenUsage } from "@/types/agent";
import { convertToBlob } from "@/utils";
import {
  ChatQueryOptions,
  ChatResponse,
  PineconeChatResponse,
  PineconeTokenUsage,
} from "./types";

// ============================================================================
// PINECONE ASSISTANT CONFIGURATION
// ============================================================================

interface PineconeConfig {
  apiKey: string;
  assistantName: string;
  timeout: number;
  chatTimeout: number;
  retryAttempts: number;
  retryDelay: number;
}

// Create dedicated logger for Pinecone service
const logger = new WinstonLogger("PineconeAssistantService");

const getPineconeConfig = (): PineconeConfig => {
  const apiKey = pineconeApiKey;
  const assistantName = pineconeAssistantName;
  const timeout = parseInt(process.env.PINECONE_TIMEOUT || "60000", 10); // Increased from 30s to 60s
  const chatTimeout = parseInt(
    process.env.PINECONE_CHAT_TIMEOUT || "45000",
    10
  ); // 45s for chat queries
  const retryAttempts = parseInt(
    process.env.PINECONE_RETRY_ATTEMPTS || "3",
    10
  );
  const retryDelay = parseInt(process.env.PINECONE_RETRY_DELAY || "2000", 10);

  if (!apiKey) {
    throw new Error("PINECONE_API_KEY environment variable is required");
  }

  if (!assistantName) {
    throw new Error("PINECONE_ASSISTANT_NAME environment variable is required");
  }

  return {
    apiKey,
    assistantName,
    timeout,
    chatTimeout,
    retryAttempts,
    retryDelay,
  };
};

// ============================================================================
// PINECONE ASSISTANT API CLIENT
// ============================================================================

// Model to use for querying Pinecone Assistant
// Updated to use Pinecone-supported model name
const QUERY_MODEL = "claude-3-5-sonnet";

/**
 * Map our internal model names to Pinecone-supported model names
 * Pinecone supports: gpt-4o, gpt-4.1, o4-mini, claude-3-5-sonnet, claude-3-7-sonnet, gemini-2.5-pro
 */
function mapToPineconeModel(modelName?: string): string {
  if (!modelName) return QUERY_MODEL;

  // Direct mapping for supported models
  const pineconeModelMap: Record<string, string> = {
    // Anthropic models
    "claude-sonnet-4-20250514": "claude-3-5-sonnet",
    "claude-3-7-sonnet-20250219": "claude-3-7-sonnet",
    "claude-opus-4-20250514": "claude-3-5-sonnet", // Map to closest supported
    "claude-opus-4-1-20250805": "claude-3-5-sonnet", // Map to closest supported

    // Google models
    "gemini-2.5-flash": "gemini-2.5-pro", // Map to closest supported
    "gemini-2.5-pro": "gemini-2.5-pro",

    // Already supported models (pass through)
    "claude-3-5-sonnet": "claude-3-5-sonnet",
    "claude-3-7-sonnet": "claude-3-7-sonnet",
    "gpt-4o": "gpt-4o",
    "gpt-4.1": "gpt-4.1",
    "o4-mini": "o4-mini",
  };

  const mappedModel = pineconeModelMap[modelName];
  if (mappedModel) {
    return mappedModel;
  }

  // Fallback to default if no mapping found
  logger.warn("Unknown model for Pinecone, using default", {
    requestedModel: modelName,
    defaultModel: QUERY_MODEL,
  });

  return QUERY_MODEL;
}

class PineconeAssistantClient {
  private config: PineconeConfig;
  private baseUrl: string;
  private chatApiUrl: string;

  constructor() {
    this.config = getPineconeConfig();
    // Base URL for Pinecone Assistant API - configurable by region
    this.baseUrl = pineconeAssistantHost;
    // Chat API URL for generating responses with citations
    this.chatApiUrl = `${pineconeAssistantHost}/assistant/chat/${pineconeAssistantName}`;

    if (!this.baseUrl) {
      throw new Error(
        "PINECONE_ASSISTANT_HOST environment variable is required"
      );
    }
  }

  /**
   * Upload document to Pinecone Assistant
   * Supports both direct file uploads and content uploads
   */
  async uploadDocument(
    payload: UnifiedDocumentPayload
  ): Promise<PlatformUploadResult> {
    const startTime = Date.now();
    const logContext = {
      file_id: payload.metadata.file_id,
      user_id: payload.metadata.user_id,
      project_id: payload.metadata.project_id,
      filename: payload.metadata.filename,
    };

    try {
      logger.info("Starting document upload to Pinecone Assistant", {
        ...logContext,
        has_file_stream: !!payload.file_stream,
        has_content: !!payload.content,
      });

      // Validate input
      if (!payload.file_stream && !payload.content) {
        const errorMsg = "Either file_stream or content must be provided";
        logger.error("Document upload validation failed", {
          ...logContext,
          error: errorMsg,
          processing_time_ms: Date.now() - startTime,
        });
        return this.createErrorResponse(errorMsg, startTime);
      }

      // Create FormData for file upload
      const formData = new FormData();

      // Add file to form data
      await this.addFileToFormData(formData, payload);

      // Add simplified metadata
      const metadata = this.buildPineconeMetadata(payload.metadata);

      // Make API request with metadata as query parameter (per Pinecone API docs)
      const response = await this.makeApiRequestWithMetadata(
        formData,
        metadata
      );

      // Parse response
      const result = await this.parseResponse(response, logContext, startTime);

      const processingTime = Date.now() - startTime;

      if (!response.ok) {
        logger.error("Pinecone Assistant upload failed", {
          ...logContext,
          status: response.status,
          error_response: result,
          processing_time_ms: processingTime,
        });

        return {
          success: false,
          error:
            result.message || `HTTP ${response.status}: ${response.statusText}`,
          processing_time_ms: processingTime,
        };
      }

      logger.info("Document uploaded successfully to Pinecone Assistant", {
        ...logContext,
        document_id: result.id,
        processing_time_ms: processingTime,
      });

      // Note: File verification removed for production - upload success is sufficient

      return {
        success: true,
        document_id: result.id,
        processing_time_ms: processingTime,
      };
    } catch (error) {
      return this.handleError(error, logContext, startTime, "upload");
    }
  }

  /**
   * Delete document from Pinecone Assistant
   */
  async deleteDocument(
    documentId: string,
    userId: string,
    projectId: string
  ): Promise<PlatformUploadResult> {
    const startTime = Date.now();
    const logContext = {
      document_id: documentId,
      user_id: userId,
      project_id: projectId,
    };

    try {
      logger.info("Deleting document from Pinecone Assistant", logContext);

      const response = await fetch(
        `${this.baseUrl}/assistant/files/${this.config.assistantName}/${documentId}`,
        {
          method: "DELETE",
          headers: {
            "Api-Key": this.config.apiKey,
          },
          signal: AbortSignal.timeout(this.config.timeout),
        }
      );

      const processingTime = Date.now() - startTime;

      if (!response.ok) {
        const result = await response
          .json()
          .catch(() => ({ message: "Unknown error" }));

        logger.error("Pinecone Assistant delete failed", {
          ...logContext,
          status: response.status,
          error: result,
          processing_time_ms: processingTime,
        });

        return {
          success: false,
          error:
            result.message || `HTTP ${response.status}: ${response.statusText}`,
          processing_time_ms: processingTime,
        };
      }

      logger.info("Document deleted successfully from Pinecone Assistant", {
        ...logContext,
        processing_time_ms: processingTime,
      });

      return {
        success: true,
        processing_time_ms: processingTime,
      };
    } catch (error) {
      return this.handleError(error, logContext, startTime, "delete");
    }
  }

  /**
   * Query Pinecone Assistant Chat API for responses with citations
   * Returns generated responses with structured citations
   */
  async queryChat(options: ChatQueryOptions): Promise<ChatResponse> {
    return this.executeWithRetry(
      () => this.performChatQuery(options),
      "Pinecone Chat Query",
      this.config.retryAttempts
    );
  }

  /**
   * Perform the actual chat query (extracted for retry logic)
   */
  private async performChatQuery(
    options: ChatQueryOptions
  ): Promise<ChatResponse> {
    const {
      messages,
      fileIds,
      userId,
      projectId,
      streaming = false,
      filter,
      model,
    } = options;

    const startTime = Date.now();
    const logContext = {
      messageCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content?.substring(0, 50),
      fileIds,
      userId,
      projectId,
    };

    let requestBody: any;
    try {
      // Map the model to Pinecone-supported model name
      const pineconeModel = mapToPineconeModel(model);

      // Build request body
      requestBody = {
        messages,
        model: pineconeModel,
        streaming,
        include_highlights: true,
      };

      logger.info("Querying Pinecone Chat API", {
        ...logContext,
        url: this.chatApiUrl,
        ...requestBody,
      });

      // CRITICAL: Add metadata filter for multi-tenancy and access control
      // This ensures user can ONLY access their own files in their projects
      const metadataFilter: any = {};

      // Always filter by user_id for security
      if (userId) {
        metadataFilter.user_id = userId;
      }

      // Always filter by project_id for multi-tenancy
      if (projectId) {
        metadataFilter.project_id = projectId;
      }

      // Optional: Filter by specific file IDs within the user's scope
      if (fileIds && fileIds.length > 0) {
        if (fileIds.length === 1) {
          metadataFilter.file_id = fileIds[0];
        } else {
          metadataFilter.file_id = { $in: fileIds };
        }
      }

      // Build the filter using MongoDB-style operators
      if (Object.keys(metadataFilter).length > 0) {
        requestBody.filter = metadataFilter;

        logger.info("Applying metadata filter for access control", {
          filter: metadataFilter,
          fileCount: fileIds?.length || 0,
          userId,
          projectId,
        });
      } else {
        logger.warn("No metadata filter applied - potential security risk", {
          userId,
          projectId,
          fileIds,
        });
      }

      // Merge with any additional filter options (but don't override security filters)
      if (filter) {
        requestBody.filter = { ...requestBody.filter, ...filter };
      }

      // Make API request with configurable timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        this.config.chatTimeout
      );

      const response = await fetch(this.chatApiUrl, {
        method: "POST",
        headers: {
          "Api-Key": this.config.apiKey,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Chat API failed: ${response.status} - ${errorText}`);
      }

      const result: PineconeChatResponse = await response.json();
      const processingTime = Date.now() - startTime;

      logger.info("Chat API query successful", {
        ...logContext,
        citationCount: result.citations?.length || 0,
        responseLength: result.message?.content?.length || 0,
        processingTime,
        finishReason: result.finish_reason,
        model: result.model,
        usage: this.mapPineconeTokenUsageToTokenUsage(result.usage),
      });

      return {
        citations: result.citations,
        finish_reason: result.finish_reason,
        id: result.id,
        message: {
          content: result.message.content,
          role: result.message.role,
        },
        model: result.model,
        usage: this.mapPineconeTokenUsageToTokenUsage(result.usage),
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;

      logger.error("Chat API query failed", {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
        errorType:
          error instanceof Error ? error.constructor.name : typeof error,
        isTimeout: error instanceof Error && error.name === "AbortError",
        processingTime,
        requestSize: JSON.stringify(requestBody).length,
        filterApplied: !!requestBody.filter,
        timeout: this.config.chatTimeout,
      });

      throw error;
    }
  }

  // ============================================================================
  // RETRY LOGIC
  // ============================================================================

  /**
   * Execute operation with retry logic and exponential backoff
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxAttempts: number = 3
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain errors (auth, validation, etc.)
        if (this.isNonRetryableError(error)) {
          throw error;
        }

        if (attempt === maxAttempts) {
          logger.error(
            `${operationName} failed after ${maxAttempts} attempts`,
            {
              error: lastError.message,
              attempts: maxAttempts,
            }
          );
          throw lastError;
        }

        const delay = Math.min(
          this.config.retryDelay * Math.pow(2, attempt - 1),
          10000
        ); // Max 10s delay
        logger.warn(
          `${operationName} attempt ${attempt} failed, retrying in ${delay}ms`,
          {
            error: lastError.message,
            attempt,
            nextDelay: delay,
            isTimeout: lastError.name === "AbortError",
          }
        );

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Check if error should not be retried
   */
  private isNonRetryableError(error: unknown): boolean {
    if (!(error instanceof Error)) return false;

    const errorMessage = error.message.toLowerCase();
    return (
      errorMessage.includes("auth") ||
      errorMessage.includes("unauthorized") ||
      errorMessage.includes("forbidden") ||
      errorMessage.includes("validation") ||
      errorMessage.includes("400") ||
      errorMessage.includes("401") ||
      errorMessage.includes("403")
    );
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Add file to FormData based on payload type
   */
  private async addFileToFormData(
    formData: FormData,
    payload: UnifiedDocumentPayload
  ): Promise<void> {
    if (payload.file_stream) {
      const fileBlob = await convertToBlob(
        payload.file_stream,
        payload.metadata.filename
      );
      formData.append("file", fileBlob, payload.metadata.filename);
    } else if (payload.content) {
      const filename = this.ensureTextFileExtension(payload.metadata.filename);
      const fileBlob = new Blob([payload.content], { type: "text/plain" });
      formData.append("file", fileBlob, filename);
    }
  }

  /**
   * Ensure filename has .txt extension for content uploads
   */
  private ensureTextFileExtension(filename: string): string {
    return filename.endsWith(".txt") ? filename : `${filename}.txt`;
  }

  /**
   * Make API request to Pinecone Assistant with metadata as query parameter
   */
  private async makeApiRequestWithMetadata(
    formData: FormData,
    metadata: Record<string, any>
  ): Promise<Response> {
    const url = this.buildUploadUrl(metadata);

    return await fetch(url.toString(), {
      method: "POST",
      headers: {
        "Api-Key": this.config.apiKey,
      },
      body: formData,
      signal: AbortSignal.timeout(this.config.timeout),
    });
  }

  /**
   * Build upload URL with metadata as query parameter
   */
  private buildUploadUrl(metadata: Record<string, any>): URL {
    const url = new URL(
      `${this.baseUrl}/assistant/files/${this.config.assistantName}`
    );

    if (Object.keys(metadata).length > 0) {
      url.searchParams.set("metadata", JSON.stringify(metadata));
    }

    return url;
  }

  /**
   * Parse API response with proper error handling
   */
  private async parseResponse(
    response: Response,
    logContext: Record<string, any>,
    startTime: number
  ): Promise<any> {
    try {
      return await response.json();
    } catch (jsonError) {
      const responseText = await response.text();
      logger.error("Failed to parse Pinecone Assistant response", {
        ...logContext,
        status: response.status,
        response_text: responseText,
        processing_time_ms: Date.now() - startTime,
      });
      logger.stack(jsonError, "error");
      return { message: `Non-JSON response: ${responseText}` };
    }
  }

  /**
   * Build Pinecone-specific metadata from unified metadata
   * IMPORTANT: Pinecone doesn't support null/undefined metadata values
   */
  private buildPineconeMetadata(
    metadata: UnifiedDocumentMetadata
  ): Record<string, any> {
    // Start with core required fields
    const pineconeMetadata: Record<string, any> = {
      user_id: metadata.user_id,
      project_id: metadata.project_id,
      file_id: metadata.file_id,
      filename: metadata.filename,
      category: metadata.category,
    };

    // Add optional audio/video metadata
    this.addAudioVideoMetadata(pineconeMetadata, metadata.audio_video);

    // Add optional tags
    this.addTagsMetadata(pineconeMetadata, metadata.tags);

    // Add custom metadata
    this.addCustomMetadata(pineconeMetadata, metadata.custom);

    // Remove null/undefined values (required by Pinecone)
    const cleanMetadata = this.removeNullValues(pineconeMetadata);

    logger.debug("Built Pinecone metadata", {
      metadata_keys: Object.keys(cleanMetadata),
      has_required_fields: !!(
        cleanMetadata.file_id &&
        cleanMetadata.user_id &&
        cleanMetadata.project_id
      ),
    });

    return cleanMetadata;
  }

  /**
   * Add audio/video metadata if present
   */
  private addAudioVideoMetadata(
    target: Record<string, any>,
    audioVideo?: UnifiedDocumentMetadata["audio_video"]
  ): void {
    if (!audioVideo) return;

    if (audioVideo.speakers?.length > 0) {
      target.speakers = audioVideo.speakers.join(",");
    }
    if (audioVideo.audio_duration) {
      target.audio_duration = audioVideo.audio_duration;
    }
    if (audioVideo.language) {
      target.language = audioVideo.language;
    }
  }

  /**
   * Add tags metadata if present
   */
  private addTagsMetadata(target: Record<string, any>, tags?: string[]): void {
    if (tags?.length > 0) {
      target.tags = tags.join(",");
    }
  }

  /**
   * Add custom metadata if present
   */
  private addCustomMetadata(
    target: Record<string, any>,
    custom?: Record<string, any>
  ): void {
    if (!custom || Object.keys(custom).length === 0) return;

    Object.entries(custom).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        target[key] = value;
      }
    });
  }

  /**
   * Remove null/undefined values as required by Pinecone
   */
  private removeNullValues(metadata: Record<string, any>): Record<string, any> {
    const clean: Record<string, any> = {};
    Object.entries(metadata).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        clean[key] = value;
      }
    });
    return clean;
  }

  /**
   * Create consistent error response
   */
  private createErrorResponse(
    error: string,
    startTime: number
  ): PlatformUploadResult {
    return {
      success: false,
      error,
      processing_time_ms: Date.now() - startTime,
    };
  }

  /**
   * Handle errors consistently across methods
   */
  private handleError(
    error: unknown,
    logContext: Record<string, any>,
    startTime: number,
    operation: string
  ): PlatformUploadResult {
    const processingTime = Date.now() - startTime;

    logger.error(`Pinecone Assistant ${operation} error`, {
      ...logContext,
      processing_time_ms: processingTime,
    });
    logger.stack(error, "error");

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      processing_time_ms: processingTime,
    };
  }

  /**
   * Map Pinecone's raw token usage to our standard TokenUsage interface
   */
  private mapPineconeTokenUsageToTokenUsage(
    pineconeUsage: PineconeTokenUsage
  ): TokenUsage {
    return {
      inputTokens: pineconeUsage.prompt_tokens,
      outputTokens: pineconeUsage.completion_tokens,
      totalTokens: pineconeUsage.total_tokens,
    };
  }
}

// ============================================================================
// SINGLETON INSTANCE & EXPORTS
// ============================================================================

export const pineconeAssistant = new PineconeAssistantClient();

/**
 * Upload document to Pinecone Assistant
 * Direct access to the singleton instance method
 */
export const uploadDocumentToPinecone =
  pineconeAssistant.uploadDocument.bind(pineconeAssistant);

/**
 * Delete document from Pinecone Assistant
 * Direct access to the singleton instance method
 */
export const deleteDocumentFromPinecone =
  pineconeAssistant.deleteDocument.bind(pineconeAssistant);

/**
 * Query Pinecone Assistant Chat API with citations
 * Direct access to the singleton instance method
 */
export const queryPineconeChat = pineconeAssistant.queryChat.bind(
  pineconeAssistant
) as (options: ChatQueryOptions) => Promise<ChatResponse>;
